import { defaultChartLineConfig, defaultChartBarConfig } from '@/views/qms/billboard/config'
import {
  CHART_TITLE_BOTTOM,
  CHART_TITLE_FONT_SIZE,
  CHART_AXIS_COLOR,
  CHART_DATA_ZOOM_SLIDER_WIDTH,
  CHART_DATA_ZOOM_SLIDER_RIGHT,
  CHART_DATA_ZOOM_MAX_VALUE_SPAN,
} from '@/views/qms/billboard/constant'

import { tooltipFormatter } from './mfgdi'

export const getLineConfig = function (data): echarts.EChartsOption {
  console.log('ddd ==> fpy-getLineConfig', data)
  const titleText = '日'
  // todo graphic
  return {
    ...defaultChartLineConfig,
    legend: {
      show: false,
    },
    title: {
      ...defaultChartLineConfig.title,
      text: titleText,
      left: 20,
      bottom: CHART_TITLE_BOTTOM,
      textStyle: {
        fontSize: CHART_TITLE_FONT_SIZE,
        fontWeight: 'normal',
        color: CHART_AXIS_COLOR,
      },
    },
    tooltip: {
      ...defaultChartLineConfig.tooltip,
      trigger: 'axis',
      formatter: tooltipFormatter,
    },
    xAxis: {
      ...defaultChartLineConfig.xAxis,
      type: 'category',
      offset: 2,
      data: ['6/14', '6/15', '6/16', '6/17', '6/18', '6/19', '6/20'],
    },
    yAxis: {
      ...defaultChartLineConfig.yAxis,
      type: 'value',
    },
    series: [
      {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        name: '实际',
        type: 'line',
        smooth: true,
        showSymbol: false,
        color: '#0189FF',
        lineStyle: {
          color: '#0189FF',
          width: 3,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(1, 137, 255, 0.2)', // 起始颜色及透明度
              },
              {
                offset: 0.1,
                color: 'rgba(1, 137, 255, 0.1)',
              },
              {
                offset: 1,
                color: 'rgba(255,255,255, 0.05)', // 结束颜色及透明度
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  }
}

export const getYBarConfig = function (data): echarts.EChartsOption {
  console.log('ddd ==> fpy-getYBarConfig', data)
  const yAxisData = [
    'Assssss',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ]

  const endValue = yAxisData.length <= 5 ? 100 : ((5 - 1) / yAxisData.length) * 100

  return {
    ...defaultChartBarConfig,
    legend: {
      show: false,
    },
    tooltip: {
      ...defaultChartLineConfig.tooltip,
      trigger: 'axis',
      formatter: tooltipFormatter,
    },
    yAxis: {
      ...defaultChartBarConfig.yAxis,
      type: 'category',
      data: yAxisData,
    },
    xAxis: {
      ...defaultChartBarConfig.xAxis,
      type: 'value',
      min: 0,
      axisLabel: {
        align: 'center',
      },
    },
    series: [
      {
        color: '#4A9EFF',
        name: '不良工站',
        type: 'bar',
        barWidth: 12,
        data: [10, 52, 200, 334, 390, 330, 220, 10, 52, 200, 334, 390, 330, 220],
      },
    ],
    dataZoom: [
      {
        type: 'slider',
        yAxisIndex: 0,
        filterMode: 'empty',
        start: 100,
        end: 100 - endValue,
        disabled: true,
        showDetail: false,
        backgroundColor: '#f5f7fa',
        borderRadius: CHART_DATA_ZOOM_SLIDER_WIDTH / 2,
        width: CHART_DATA_ZOOM_SLIDER_WIDTH,
        right: CHART_DATA_ZOOM_SLIDER_RIGHT,
        maxValueSpan: CHART_DATA_ZOOM_MAX_VALUE_SPAN,
        handleSize: CHART_DATA_ZOOM_SLIDER_WIDTH,
        handleIcon: `M256.5 32.4c-123.5 0-223.6 100.1-223.6 223.6s100.1 223.6 223.6 223.6 223.6-100.1 223.6-223.6S380 32.4 256.5 32.4z M206 157l0 199c0 0 1.45 8.2 9.9 8.2s9.55-8.2 9.55-8.2l0-199c0 0-1.02-8.7-9.55-8.7C207 148.3 206 157 206 157z M286.3 157l0 199c0 0 1.45 8.2 9.9 8.2s9.55-8.2 9.55-8.2l0-199c0 0-1.02-8.7-9.55-8.7C287.3 148.3 286.3 157 286.3 157z`,
        fillerColor: '#eceff2',
        brushSelect: false,
        zoomLock: false,
      },
      {
        type: 'inside',
        yAxisIndex: 0,
        zoomOnMouseWheel: false, // 禁用鼠标滚轮缩放
        moveOnMouseWheel: true, // 启用鼠标滚轮平移
        moveOnMouseMove: true, // 启用鼠标拖动平移
      },
    ],
  }
}
