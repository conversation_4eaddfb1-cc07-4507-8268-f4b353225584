export interface TabListItem {
  title: string
  id: string
}
export interface ExpandItem {
  id: string
  title: string
}
export interface ProjectItem {
  projectCode: string
  withinTwoWeek: number
  remainDays: null | number
  nextTr: string
  marketDays: null | number
  indexNodeList: string[]
}
export interface ChartTheme {
  color: string[] | Record<string, string | string[]>
  icons?: string[]
}
// 通用图表数据项接口
export interface ChartDataItem {
  name: string
  value: number
  color?: string
}

// 通用图表数据结构接口 todo
export interface ChartData<T extends ChartDataItem> {
  chartData: {
    dataList: T[]
  }
}

// 饼图数据项接口
export type PieChartDataItem = ChartDataItem

// 柱状图数据项接口
export type BarChartDataItem = ChartDataItem

// 饼图和柱状图数据类型
export type PieChartData = ChartData<PieChartDataItem>
export type BarChartData = ChartData<BarChartDataItem>
export type LineChartData = ChartData<BarChartDataItem>
