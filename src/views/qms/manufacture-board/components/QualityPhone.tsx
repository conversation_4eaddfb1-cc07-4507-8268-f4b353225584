import React, { memo, useState, useMemo } from 'react'
import styled from 'styled-components'
import MfgDi from './MFGDI'
import CardLine, { IconTooltip } from './CardLine'
import {
  CARD_NAME,
  CARD_STYLE,
  BOARD_TYPE,
  CHART_TYPE,
  FONT_WEIGHT_MEDIUM,
  FONT_SIZE_NORMAL,
  FONT_SIZE_TITLE,
} from '../constant'
import { CardRadius } from './Widgets'
import { NavButtonText } from './NavButton'
import { ContentParams } from './BoardLine'
import BoardChart from './BoardChart'

import { blueTheme, greenTheme } from '../config'
import { EXTRA_LINE_CONFIG } from '@/views/qms/billboard/constant'

const TitleWapper = styled.div`
  display: flex;
  align-items: center;
  font-weight: ${FONT_WEIGHT_MEDIUM};
  font-size: ${FONT_SIZE_NORMAL};
  line-height: ${FONT_SIZE_TITLE};
  color: ${CARD_STYLE.TITLE_COLOR};
`

const FPY_PY = [
  {
    name: 'FPY',
    key: BOARD_TYPE.FPY,
    tooltip: 'FPY(First Pass Yield) 生产一次良率，指一次性通过所有测试的良品数占总试产数的比例',
  },
  {
    name: 'PY',
    key: BOARD_TYPE.PY,
    tooltip:
      'PY(Pass Yield) 生产最终良率，也为最终通过率，指最终通过所有测试的良品数占总试产数的比例',
  },
  {
    name: '不良工站',
    key: BOARD_TYPE.BAD_STATION,
    tooltip: '',
    tabs: [BOARD_TYPE.FPY, BOARD_TYPE.PY],
  },
]

const OBA = [
  {
    name: '成品检验DPPM',
    key: BOARD_TYPE.DPPM,
    tooltip: [
      'OBA(Out Of Box Audit，开箱检验）是对包装好的产品进行开箱检验，检查产品及配件的外观、功能、包装等是否符合质量标准',
      'DPPM(Defective Pieces Per Million，每百万个产品中的不良数) 衡量抽检产品中不良产品的缺陷程度，DPPM=10^6*（CR*3+MA*1+MI*0.3）/抽检数量，CR、MA、MI分别表示不良产品缺陷严重等级为Critical、Major、Minor的产品数量',
      'OBA-DPPM：指的是开箱检验发现的不良产品缺陷程度',
    ],
  },
  {
    name: '成品检验LAR',
    key: BOARD_TYPE.LAR,
    tooltip: [
      'OBA(Out Of Box Audi）是对包装好的产品进行开箱检验，检查产品及配件的外观、功能、包装等是否符合质量标准',
      'LAR (Lot Acceptance Rate) 是检验批合格率，LAR=（合格批数/总检验批数) * 100%',
      'OBA-LAR：指的是开箱检验的批合格率',
    ],
  },
  {
    name: '不良现象',
    key: BOARD_TYPE.BAD_BEHAVIOR,
  },
]

export const boardConfig = {
  [BOARD_TYPE.FPY]: {
    chartType: CHART_TYPE.FPY_LINE,
    theme: blueTheme,
    extraLine: EXTRA_LINE_CONFIG,
  },
  [BOARD_TYPE.PY]: {
    chartType: CHART_TYPE.FPY_LINE,
    theme: greenTheme,
    extraLine: EXTRA_LINE_CONFIG,
  },
  [BOARD_TYPE.BAD_STATION]: {
    chartType: CHART_TYPE.FPY_BAR,
  },
  [BOARD_TYPE.DPPM]: {
    chartType: CHART_TYPE.FPY_LINE,
  },
  [BOARD_TYPE.LAR]: {
    chartType: CHART_TYPE.FPY_LINE,
  },
  [BOARD_TYPE.BAD_BEHAVIOR]: {
    chartType: CHART_TYPE.FPY_BAR,
  },
}

const mocks = [
  {
    name: CARD_NAME.FPY_PY,
    list: FPY_PY,
  },
  {
    name: CARD_NAME.OBA,
    list: OBA,
  },
]

const ComponentCard = ({ data: item }) => {
  const [modeType, setModeType] = useState<string>(BOARD_TYPE.FPY)

  return (
    <CardRadius key={item.name} className="flex-1">
      <div className="flex justify-between">
        <TitleWapper>
          <span>{item.name}</span>
          {item.tooltip && <IconTooltip text={item.tooltip} />}
        </TitleWapper>
        {item.tabs && <NavButtonText list={item.tabs} setActive={setModeType} active={modeType} />}
      </div>

      <BoardChart
        data={{ chartData: { dataList: [] } }}
        config={boardConfig[item.key]}
        controlXText={[]}
        width="100%"
        height="216px"
        lastPeriod={''}
      />
    </CardRadius>
  )
}

const ComponentFpy = memo(() => {
  return (
    <>
      {mocks.map((item) => {
        return (
          <div key={item.name} className="prd-boardcell">
            <CardLine title={item.name}></CardLine>
            <div className="flex gap-8">
              {item.list.map((child) => {
                return <ComponentCard key={child.key} data={child} />
              })}
            </div>
          </div>
        )
      })}
    </>
  )
})
ComponentFpy.displayName = 'ComponentFpy'

const QualityMfgdi: React.FC<ContentParams> = function (props) {
  return (
    <>
      <MfgDi {...props} />
      <ComponentFpy />
    </>
  )
}

export default QualityMfgdi
